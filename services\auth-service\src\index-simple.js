// 🎓 LEARNING: Simplified Auth Service for testing without external dependencies

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// 🚀 Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// 🛡️ Security and middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 🎓 LEARNING: In-memory storage for testing (replace with database in production)
const users = new Map();
const otpCodes = new Map();

// 🎓 LEARNING: Simple OTP generation
function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 🏥 Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'auth-service',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    mode: 'simplified-testing'
  });
});

// 📱 POST /api/auth/register - User registration
app.post('/api/auth/register', (req, res) => {
  try {
    const { phoneNumber, firstName, lastName, password, userType, email } = req.body;

    // 🔍 Basic validation
    if (!phoneNumber || !firstName || !lastName || !password || !userType) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Check if user exists
    if (users.has(phoneNumber)) {
      return res.status(409).json({
        success: false,
        message: 'User with this phone number already exists'
      });
    }

    // 👤 Create user
    const user = {
      id: Date.now().toString(),
      phoneNumber,
      firstName,
      lastName,
      password, // In production, this would be hashed
      userType,
      email,
      isPhoneVerified: false,
      createdAt: new Date().toISOString()
    };

    users.set(phoneNumber, user);

    // 📱 Generate and store OTP
    const otp = generateOTP();
    otpCodes.set(phoneNumber, {
      otp,
      expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes
      attempts: 0
    });

    console.log(`✅ User registered: ${phoneNumber} (${userType})`);
    console.log(`📱 OTP for ${phoneNumber}: ${otp}`); // In production, this would be sent via SMS

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please verify your phone number.',
      data: {
        userId: user.id,
        phoneNumber: user.phoneNumber,
        userType: user.userType,
        isPhoneVerified: user.isPhoneVerified,
        otpSent: true
      },
      // 🎓 LEARNING: For testing purposes, we include the OTP in response
      testOTP: otp
    });

  } catch (error) {
    console.error('❌ Registration failed:', error.message);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
});

// 🔐 POST /api/auth/login - User login
app.post('/api/auth/login', (req, res) => {
  try {
    const { phoneNumber, password } = req.body;

    if (!phoneNumber || !password) {
      return res.status(400).json({
        success: false,
        message: 'Phone number and password are required'
      });
    }

    // 🔍 Find user
    const user = users.get(phoneNumber);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    // 🔑 Verify password (in production, use bcrypt)
    if (user.password !== password) {
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    // 📱 Check if phone is verified
    if (!user.isPhoneVerified) {
      // Generate new OTP
      const otp = generateOTP();
      otpCodes.set(phoneNumber, {
        otp,
        expiresAt: Date.now() + 5 * 60 * 1000,
        attempts: 0
      });

      console.log(`📱 OTP for verification: ${otp}`);

      return res.status(403).json({
        success: false,
        message: 'Phone number not verified. OTP sent for verification.',
        requiresVerification: true,
        testOTP: otp
      });
    }

    // 🎫 Generate simple token (in production, use JWT)
    const token = `token_${user.id}_${Date.now()}`;

    console.log(`✅ User logged in: ${phoneNumber}`);

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          phoneNumber: user.phoneNumber,
          firstName: user.firstName,
          lastName: user.lastName,
          userType: user.userType,
          isPhoneVerified: user.isPhoneVerified
        },
        token
      }
    });

  } catch (error) {
    console.error('❌ Login failed:', error.message);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

// 📱 POST /api/auth/send-otp - Send OTP
app.post('/api/auth/send-otp', (req, res) => {
  try {
    const { phoneNumber } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required'
      });
    }

    // 🔍 Check if user exists
    const user = users.get(phoneNumber);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // 📱 Generate OTP
    const otp = generateOTP();
    otpCodes.set(phoneNumber, {
      otp,
      expiresAt: Date.now() + 5 * 60 * 1000,
      attempts: 0
    });

    console.log(`📱 OTP sent to ${phoneNumber}: ${otp}`);

    res.status(200).json({
      success: true,
      message: 'OTP sent successfully',
      expiresIn: 300,
      testOTP: otp // For testing only
    });

  } catch (error) {
    console.error('❌ Send OTP failed:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to send OTP',
      error: error.message
    });
  }
});

// ✅ POST /api/auth/verify-otp - Verify OTP
app.post('/api/auth/verify-otp', (req, res) => {
  try {
    const { phoneNumber, otp } = req.body;

    if (!phoneNumber || !otp) {
      return res.status(400).json({
        success: false,
        message: 'Phone number and OTP are required'
      });
    }

    // 🔍 Find user
    const user = users.get(phoneNumber);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // 🔍 Check OTP
    const storedOTP = otpCodes.get(phoneNumber);
    if (!storedOTP) {
      return res.status(400).json({
        success: false,
        message: 'OTP expired or not found'
      });
    }

    // Check expiration
    if (Date.now() > storedOTP.expiresAt) {
      otpCodes.delete(phoneNumber);
      return res.status(400).json({
        success: false,
        message: 'OTP expired'
      });
    }

    // Verify OTP
    if (storedOTP.otp !== otp) {
      storedOTP.attempts += 1;
      if (storedOTP.attempts >= 3) {
        otpCodes.delete(phoneNumber);
        return res.status(400).json({
          success: false,
          message: 'Too many failed attempts. Please request a new OTP.'
        });
      }
      return res.status(400).json({
        success: false,
        message: `Invalid OTP. ${3 - storedOTP.attempts} attempts remaining.`
      });
    }

    // ✅ OTP verified
    user.isPhoneVerified = true;
    otpCodes.delete(phoneNumber);

    // Generate token
    const token = `token_${user.id}_${Date.now()}`;

    console.log(`✅ Phone verified: ${phoneNumber}`);

    res.status(200).json({
      success: true,
      message: 'Phone number verified successfully',
      data: {
        user: {
          id: user.id,
          phoneNumber: user.phoneNumber,
          firstName: user.firstName,
          lastName: user.lastName,
          userType: user.userType,
          isPhoneVerified: true
        },
        token
      }
    });

  } catch (error) {
    console.error('❌ OTP verification failed:', error.message);
    res.status(500).json({
      success: false,
      message: 'OTP verification failed',
      error: error.message
    });
  }
});

// 🔍 GET /api/auth/users - List all users (for testing)
app.get('/api/auth/users', (req, res) => {
  const userList = Array.from(users.values()).map(user => ({
    id: user.id,
    phoneNumber: user.phoneNumber,
    firstName: user.firstName,
    lastName: user.lastName,
    userType: user.userType,
    isPhoneVerified: user.isPhoneVerified
  }));

  res.status(200).json({
    success: true,
    data: userList,
    count: userList.length
  });
});

// 🚫 Handle 404 errors
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// 🚀 Start the server
app.listen(PORT, () => {
  console.log(`🚀 Auth Service (Simplified) running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 Auth API: http://localhost:${PORT}/api/auth`);
  console.log(`👥 Users list: http://localhost:${PORT}/api/auth/users`);
  console.log('');
  console.log('🎓 LEARNING: This is a simplified version for testing');
  console.log('📱 OTP codes will be displayed in console for testing');
});

module.exports = app;
