// 🎓 LEARNING: This is the main entry point for our Auth Service

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import our custom modules
const connectDB = require('./config/database');
const connectRedis = require('./config/redis');
const authRoutes = require('./routes/auth');
const { errorHandler } = require('./middleware/errorHandler');

// 🚀 Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// 🎓 LEARNING: Middleware functions run before route handlers
// They process requests in the order they're defined

// 🛡️ Security middleware
app.use(helmet()); // Sets various HTTP headers for security
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// 📊 Logging middleware
app.use(morgan('combined'));

// 🚦 Rate limiting to prevent abuse
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// 📝 Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 🏥 Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'auth-service',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// 🔐 Authentication routes
app.use('/api/auth', authRoutes);

// 🎓 LEARNING: Error handling middleware should be last
app.use(errorHandler);

// 🚫 Handle 404 errors
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// 🎓 LEARNING: Async function to start the server
async function startServer() {
  try {
    // 🗄️ Connect to MongoDB
    await connectDB();
    console.log('✅ Connected to MongoDB');

    // ⚡ Connect to Redis
    await connectRedis();
    console.log('✅ Connected to Redis');

    // 🌐 Start the server
    app.listen(PORT, () => {
      console.log(`🚀 Auth Service running on port ${PORT}`);
      console.log(`🏥 Health check: http://localhost:${PORT}/health`);
      console.log(`🔐 Auth API: http://localhost:${PORT}/api/auth`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// 🎓 LEARNING: Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// 🚀 Start the server
startServer();

module.exports = app;
