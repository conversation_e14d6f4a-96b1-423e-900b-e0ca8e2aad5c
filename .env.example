# 🎓 LEARNING: Environment variables keep sensitive data secure
# Copy this file to .env and fill in your actual values

# 🇱🇰 Sri Lankan SMS Providers
DIALOG_SMS_API_KEY=your_dialog_api_key_here
DIALOG_SMS_USERNAME=your_dialog_username
DIALOG_SMS_PASSWORD=your_dialog_password

MOBITEL_SMS_API_KEY=your_mobitel_api_key_here
MOBITEL_SMS_USERNAME=your_mobitel_username
MOBITEL_SMS_PASSWORD=your_mobitel_password

# 💳 PayHere Payment Gateway (Sri Lankan)
PAYHERE_MERCHANT_ID=your_payhere_merchant_id
PAYHERE_MERCHANT_SECRET=your_payhere_merchant_secret
PAYHERE_SANDBOX=true

# 🏦 Sri Lankan Bank Gateways
SAMPATH_BANK_MERCHANT_CODE=your_sampath_merchant_code
SAMPATH_BANK_API_URL=https://api.sampath.lk/payments
SAMPATH_BANK_SECRET=your_sampath_secret

COMMERCIAL_BANK_MERCHANT_ID=your_commercial_merchant_id
COMMERCIAL_BANK_API_URL=https://api.combank.lk/payments
COMMERCIAL_BANK_SECRET=your_commercial_secret

HNB_BANK_MERCHANT_ID=your_hnb_merchant_id
HNB_BANK_API_URL=https://api.hnb.lk/payments
HNB_BANK_SECRET=your_hnb_secret

# 🔔 Firebase Cloud Messaging
FCM_SERVER_KEY=your_fcm_server_key
FCM_PROJECT_ID=your_firebase_project_id

# 🗄️ Database Configuration
MONGODB_USERNAME=admin
MONGODB_PASSWORD=password123
MONGODB_DATABASE=docapp

# 🔐 Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-refresh-token-secret
ENCRYPTION_KEY=your-32-character-encryption-key

# 🌍 Application Settings
NODE_ENV=development
API_VERSION=v1
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,si,ta

# 📍 Location Settings
DEFAULT_COUNTRY=LK
DEFAULT_CITY=Colombo
MAX_DOCTOR_DISTANCE_KM=50
LOCATION_UPDATE_INTERVAL_MS=30000

# 💰 Payment Settings
DEFAULT_CURRENCY=LKR
MIN_CONSULTATION_FEE=500
MAX_CONSULTATION_FEE=50000
PAYMENT_TIMEOUT_MINUTES=15

# 📱 Mobile App Settings
ANDROID_PACKAGE_NAME=com.docapp.srilanka
IOS_BUNDLE_ID=com.docapp.srilanka
APP_VERSION=1.0.0

# 🔧 Development Settings
LOG_LEVEL=debug
ENABLE_SWAGGER=true
ENABLE_CORS=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# 🏥 Medical Settings
EMERGENCY_REQUEST_PRIORITY=1
REGULAR_REQUEST_PRIORITY=5
MAX_ACTIVE_REQUESTS_PER_PATIENT=3
DOCTOR_RESPONSE_TIMEOUT_MINUTES=10

# 📊 Monitoring & Analytics
ENABLE_METRICS=true
ENABLE_TRACING=true
SENTRY_DSN=your_sentry_dsn_for_error_tracking
