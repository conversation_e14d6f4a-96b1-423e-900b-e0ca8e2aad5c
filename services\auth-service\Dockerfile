# 🎓 LEARNING: Dockerfile defines how to build our container

# Use official Node.js runtime as base image
FROM node:18-alpine

# 📁 Set working directory inside container
WORKDIR /app

# 📦 Copy package files first (for better Docker layer caching)
COPY package*.json ./

# 🔧 Install dependencies
RUN npm ci --only=production

# 📋 Copy application source code
COPY . .

# 👤 Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S docapp -u 1001

# 🔐 Change ownership of app directory
RUN chown -R docapp:nodejs /app
USER docapp

# 🌐 Expose port 3001
EXPOSE 3001

# 🏃 Start the application
CMD ["npm", "start"]
