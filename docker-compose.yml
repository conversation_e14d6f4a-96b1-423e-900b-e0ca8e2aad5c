version: '3.8'

# 🎓 LEARNING: Docker Compose orchestrates multiple containers
# Each service runs in its own container for isolation and scalability

services:
  # 🌐 API Gateway - Entry point for all requests
  api-gateway:
    build: ./api-gateway
    container_name: docapp-api-gateway
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - AUTH_SERVICE_URL=http://auth-service:3001
      - USER_SERVICE_URL=http://user-service:3002
    depends_on:
      - auth-service
      - user-service
    networks:
      - docapp-network

  # 🔐 Authentication Service - SMS verification & JWT tokens
  auth-service:
    build: ./services/auth-service
    container_name: docapp-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_auth
      - REDIS_URL=redis://redis-cache:6379
      - JWT_SECRET=your-super-secret-jwt-key
      - DIALOG_SMS_API_KEY=${DIALOG_SMS_API_KEY}
      - MOBITEL_SMS_API_KEY=${MOBITEL_SMS_API_KEY}
    depends_on:
      - mongodb-primary
      - redis-cache
    networks:
      - docapp-network

  # 👥 User Service - Patient & Doctor profiles
  user-service:
    build: ./services/user-service
    container_name: docapp-user-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_users
      - KAFKA_BROKERS=kafka-broker-1:9092,kafka-broker-2:9093
    depends_on:
      - mongodb-primary
      - kafka-broker-1
    networks:
      - docapp-network

  # 🏥 Request Service - Medical assistance requests
  request-service:
    build: ./services/request-service
    container_name: docapp-request-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_requests
      - KAFKA_BROKERS=kafka-broker-1:9092,kafka-broker-2:9093
    depends_on:
      - mongodb-primary
      - kafka-broker-1
    networks:
      - docapp-network

  # 📍 Location Service - Geolocation & proximity matching
  location-service:
    build: ./services/location-service
    container_name: docapp-location-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_locations
      - KAFKA_BROKERS=kafka-broker-1:9092,kafka-broker-2:9093
    depends_on:
      - mongodb-primary
      - kafka-broker-1
    networks:
      - docapp-network

  # 💳 Payment Service - PayHere & Sri Lankan banks
  payment-service:
    build: ./services/payment-service
    container_name: docapp-payment-service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_payments
      - PAYHERE_MERCHANT_ID=${PAYHERE_MERCHANT_ID}
      - PAYHERE_MERCHANT_SECRET=${PAYHERE_MERCHANT_SECRET}
      - KAFKA_BROKERS=kafka-broker-1:9092,kafka-broker-2:9093
    depends_on:
      - mongodb-primary
      - kafka-broker-1
    networks:
      - docapp-network

  # 🔔 Notification Service - Push notifications & SMS
  notification-service:
    build: ./services/notification-service
    container_name: docapp-notification-service
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_notifications
      - KAFKA_BROKERS=kafka-broker-1:9092,kafka-broker-2:9093
      - FCM_SERVER_KEY=${FCM_SERVER_KEY}
    depends_on:
      - mongodb-primary
      - kafka-broker-1
    networks:
      - docapp-network

  # 💬 Chat Service - Real-time messaging
  chat-service:
    build: ./services/chat-service
    container_name: docapp-chat-service
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_chat
      - REDIS_URL=redis://redis-cache:6379
    depends_on:
      - mongodb-primary
      - redis-cache
    networks:
      - docapp-network

  # ⭐ Review Service - Ratings & feedback
  review-service:
    build: ./services/review-service
    container_name: docapp-review-service
    ports:
      - "3008:3008"
    environment:
      - NODE_ENV=development
      - MONGODB_URL=mongodb://mongodb-primary:27017/docapp_reviews
      - KAFKA_BROKERS=kafka-broker-1:9092,kafka-broker-2:9093
    depends_on:
      - mongodb-primary
      - kafka-broker-1
    networks:
      - docapp-network

  # 🗄️ MongoDB Primary Database
  mongodb-primary:
    image: mongo:6.0
    container_name: docapp-mongodb-primary
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
    volumes:
      - mongodb_data:/data/db
    networks:
      - docapp-network

  # ⚡ Redis Cache & Sessions
  redis-cache:
    image: redis:7.0-alpine
    container_name: docapp-redis-cache
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - docapp-network

  # 🐘 Zookeeper for Kafka coordination
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: docapp-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - docapp-network

  # 📨 Kafka Broker 1
  kafka-broker-1:
    image: confluentinc/cp-kafka:7.4.0
    container_name: docapp-kafka-broker-1
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-broker-1:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - docapp-network

  # 📨 Kafka Broker 2
  kafka-broker-2:
    image: confluentinc/cp-kafka:7.4.0
    container_name: docapp-kafka-broker-2
    depends_on:
      - zookeeper
    ports:
      - "9093:9093"
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-broker-2:9093
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
    networks:
      - docapp-network

  # 🎛️ Kafka UI for management
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: docapp-kafka-ui
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: docapp-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka-broker-1:9092,kafka-broker-2:9093
    depends_on:
      - kafka-broker-1
      - kafka-broker-2
    networks:
      - docapp-network

# 🌐 Network for service communication
networks:
  docapp-network:
    driver: bridge

# 💾 Persistent data storage
volumes:
  mongodb_data:
  redis_data:
