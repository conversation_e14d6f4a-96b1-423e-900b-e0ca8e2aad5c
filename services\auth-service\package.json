{"name": "docapp-auth-service", "version": "1.0.0", "description": "Authentication service with SMS verification for DocApp", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "redis": "^4.6.7", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.5.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["authentication", "sms", "jwt", "microservice", "sri-lanka", "medical"], "author": "DocApp Team", "license": "MIT"}