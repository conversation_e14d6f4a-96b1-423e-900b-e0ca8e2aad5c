# DocApp - Sri Lankan Medical Assistance Platform

A microservices-based mobile application connecting patients with nearby doctors in Sri Lanka.

## 🏗️ Architecture Overview

This application uses a **microservices architecture** with the following components:

### Core Services
- **API Gateway** - Request routing and authentication
- **Auth Service** - SMS authentication with Dialog/Mobitel
- **User Service** - Patient and doctor profile management
- **Request Service** - Medical assistance request handling
- **Location Service** - Geolocation and proximity matching
- **Payment Service** - PayHere and Sri Lankan bank integration
- **Notification Service** - Push notifications and SMS
- **Chat Service** - Real-time messaging
- **Review Service** - Ratings and feedback

### Infrastructure
- **Kafka** - Event streaming and messaging
- **MongoDB** - Primary database
- **Redis** - Caching and sessions
- **ELK Stack** - Logging and monitoring
- **Prometheus/Grafana** - Metrics and alerting

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+
- React Native CLI
- Android Studio / Xcode

### Development Setup
```bash
# Clone the repository
git clone <repository-url>
cd DocApp

# Copy environment variables
cp .env.example .env

# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

## 📱 Mobile App
- **Platform**: React Native (iOS & Android)
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation
- **Maps**: React Native Maps
- **Payments**: PayHere SDK

## 🇱🇰 Sri Lankan Integrations
- **SMS**: Dialog & Mobitel APIs
- **Payments**: PayHere, Sampath Bank, Commercial Bank
- **Currency**: LKR support
- **Languages**: English, Sinhala, Tamil

## 📚 Learning Resources
This project is built step-by-step with detailed explanations for educational purposes.

## 🔧 Development
Each service is independently deployable and follows microservices best practices.

## 📄 License
MIT License
