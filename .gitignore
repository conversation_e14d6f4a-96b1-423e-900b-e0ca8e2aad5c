# 🎓 LEARNING: .gitignore prevents sensitive files from being committed to version control

# 🔐 Environment variables and secrets
.env
.env.local
.env.production
.env.staging
*.pem
*.key
*.crt

# 📦 Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# 🗄️ Database files
*.db
*.sqlite
*.sqlite3

# 📊 Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 🏗️ Build outputs
dist/
build/
.next/
out/

# 🧪 Testing
coverage/
.nyc_output
.jest/

# 🔧 IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# 🍎 macOS
.DS_Store
.AppleDouble
.LSOverride

# 🐧 Linux
*~
.fuse_hidden*
.directory
.Trash-*

# 🪟 Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 📱 Mobile development
# React Native
.expo/
.expo-shared/

# Android
*.apk
*.aab
android/app/build/
android/.gradle/
android/local.properties

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/*.xcuserdata

# 🐳 Docker
.docker/
docker-compose.override.yml

# ☁️ Cloud and deployment
.terraform/
*.tfstate
*.tfstate.backup
.serverless/

# 🎯 Temporary files
tmp/
temp/
.tmp/
.cache/

# 📈 Monitoring and analytics
.monitoring/
metrics/

# 🔍 Search indexes
.elasticsearch/

# 📨 Message queues
.kafka/
.rabbitmq/
