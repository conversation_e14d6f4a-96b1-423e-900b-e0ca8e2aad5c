// 🎓 LEARNING: Redis connection for caching and session management

const { createClient } = require('redis');

let redisClient;

// 🎓 LEARNING: Redis connection with error handling and reconnection
const connectRedis = async () => {
  try {
    const redisURL = process.env.REDIS_URL || 'redis://localhost:6379';
    
    // 🔧 Create Redis client with options
    redisClient = createClient({
      url: redisURL,
      retry_strategy: (options) => {
        // 🔄 Reconnection strategy
        if (options.error && options.error.code === 'ECONNREFUSED') {
          console.error('❌ Redis server refused connection');
          return new Error('Redis server refused connection');
        }
        if (options.total_retry_time > 1000 * 60 * 60) {
          console.error('❌ Redis retry time exhausted');
          return new Error('Retry time exhausted');
        }
        if (options.attempt > 10) {
          console.error('❌ Redis max retry attempts reached');
          return undefined;
        }
        // Reconnect after
        return Math.min(options.attempt * 100, 3000);
      }
    });

    // 🎓 LEARNING: Event listeners for Redis connection monitoring
    redisClient.on('error', (err) => {
      console.error('❌ Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      console.log('🔗 Redis Client Connected');
    });

    redisClient.on('ready', () => {
      console.log('✅ Redis Client Ready');
    });

    redisClient.on('end', () => {
      console.log('🛑 Redis Client Disconnected');
    });

    // 🔗 Connect to Redis
    await redisClient.connect();
    
    // 🧪 Test the connection
    await redisClient.ping();
    console.log('🏓 Redis connection test successful');

  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    throw error;
  }
};

// 🎓 LEARNING: Helper functions for Redis operations

// 📝 Store data with expiration
const setWithExpiry = async (key, value, expiryInSeconds = 3600) => {
  try {
    await redisClient.setEx(key, expiryInSeconds, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error('❌ Redis SET error:', error);
    return false;
  }
};

// 📖 Get data from Redis
const get = async (key) => {
  try {
    const value = await redisClient.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error('❌ Redis GET error:', error);
    return null;
  }
};

// 🗑️ Delete data from Redis
const del = async (key) => {
  try {
    await redisClient.del(key);
    return true;
  } catch (error) {
    console.error('❌ Redis DEL error:', error);
    return false;
  }
};

// 🔍 Check if key exists
const exists = async (key) => {
  try {
    const result = await redisClient.exists(key);
    return result === 1;
  } catch (error) {
    console.error('❌ Redis EXISTS error:', error);
    return false;
  }
};

// 🎓 LEARNING: Graceful shutdown
process.on('SIGINT', async () => {
  if (redisClient) {
    await redisClient.quit();
    console.log('🛑 Redis connection closed');
  }
});

module.exports = {
  connectRedis,
  redisClient: () => redisClient,
  setWithExpiry,
  get,
  del,
  exists
};
